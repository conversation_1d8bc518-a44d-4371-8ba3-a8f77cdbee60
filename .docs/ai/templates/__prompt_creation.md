# [PROMPT_PURPOSE] Assistant

//description of what this prompt accomplishes and its main use cases

## 📚 REQUIRED DOCUMENTATION

NOTE: Always follow the [.docs/ai/templates/*.md] when [PERFORMING_TASK].
Do not include the ./.docs folder in any code analysis or repo analysis

//list of documentation requirements and references
- [REQUIREMENT_1] is defined in [./path/to/doc.md]
- [REQUIREMENT_2] is defined in [./path/to/template.md]
- [REQUIREMENT_3] is defined in [./path/to/reference.md]

## 🔄 MANDATORY SYNC PROTOCOL

//numbered step-by-step process for the prompt execution

1. Before [PERFORMING_ACTION] read the current [.docs/ai/templates/*.md].

2. [SECOND_STEP_ACTION]:
   - **[SUB_ACTION_1]**: [Description of sub-action]
   - **[SUB_ACTION_2]**: [Description of sub-action]

3. [THIRD_STEP_ACTION] and determine if it should:
   - **[OPTION_1]**: [Description of first option]
   - **[OPTION_2]**: [Description of second option]

4. If [CONDITION_1]:
   - [ACTION_A]: [Description]
   - [ACTION_B]: [Description]
   - [ACTION_C]: [Description]

5. If [CONDITION_2]:
   - [ACTION_X]: [Description]
   - [ACTION_Y]: [Description]
   - [ACTION_Z]: [Description]

6. [VALIDATION_STEP] and validate the [OUTPUT_TYPE].

7. [FINAL_STEP] to [./path/to/output] following the [NAMING_CONVENTION].

🔄 CONTINUE THE SYNC PROTOCOL UNTIL THE [COMPLETION_CRITERIA] IS [COMPLETION_STATE].

## ‼️ [RULES_SECTION_NAME]

//description of rules and guidelines for this prompt

### ✅ Required Elements
- **[REQUIREMENT_1]**: [Description of what's required]
- **[REQUIREMENT_2]**: [Description of what's required]
- **[REQUIREMENT_3]**: [Description of what's required]
- **[REQUIREMENT_4]**: [Description of what's required]

### ✅ [POSITIVE_GUIDELINES_SECTION]
- **[GUIDELINE_1]**: [Description of positive guideline]
- **[GUIDELINE_2]**: [Description of positive guideline]
- **[GUIDELINE_3]**: [Description of positive guideline]

### ✅ [FORMATTING_OR_STRUCTURE_STANDARDS]
- **[STANDARD_1]**: [Description of formatting standard]
- **[STANDARD_2]**: [Description of formatting standard]
- **[STANDARD_3]**: [Description of formatting standard]

### ❌ Avoid These Patterns
- **[ANTI_PATTERN_1]**: [Description of what to avoid]
- **[ANTI_PATTERN_2]**: [Description of what to avoid]
- **[ANTI_PATTERN_3]**: [Description of what to avoid]

## 📋 [CATEGORIZATION_SECTION]

//description of different types or categories relevant to this prompt

### [CATEGORY_1_NAME]
Based on `[reference-file.md]`:
- [Characteristic 1]: [Description]
- [Characteristic 2]: [Description]
- [Characteristic 3]: [Description]

### [CATEGORY_2_NAME]
Based on `[reference-file.md]`:
- [Characteristic 1]: [Description]
- [Characteristic 2]: [Description]
- [Characteristic 3]: [Description]

### [CUSTOM_CATEGORY_NAME]
For [new category type]:
- [Characteristic 1]: [Description]
- [Characteristic 2]: [Description]
- [Characteristic 3]: [Description]

## 🎯 [INPUT_PROCESSING_SECTION]

//description of how to process user input or requirements

When user specifies [INPUT_TYPE], extract:

1. **[EXTRACTION_1]**: [What to extract and why]
2. **[EXTRACTION_2]**: [What to extract and why]
3. **[EXTRACTION_3]**: [What to extract and why]
4. **[EXTRACTION_4]**: [What to extract and why]
5. **[EXTRACTION_5]**: [What to extract and why]
6. **[EXTRACTION_6]**: [What to extract and why]

## 🔧 [VALIDATION_SECTION]

//description of validation process

Before finalizing [OUTPUT_TYPE], verify:

- [ ] [VALIDATION_CHECK_1]
- [ ] [VALIDATION_CHECK_2]
- [ ] [VALIDATION_CHECK_3]
- [ ] [VALIDATION_CHECK_4]
- [ ] [VALIDATION_CHECK_5]
- [ ] [VALIDATION_CHECK_6]
- [ ] [VALIDATION_CHECK_7]
- [ ] [VALIDATION_CHECK_8]

## 📝 [EXAMPLES_SECTION]

//description of examples showing proper usage

### [EXAMPLE_TYPE_1]
```markdown
# [Example Title]

## 📚 REQUIRED DOCUMENTATION
//example content showing proper usage patterns
[EXAMPLE_CONTENT]

## 🔄 MANDATORY SYNC PROTOCOL
//example protocol steps
1. [Example step 1]
2. [Example step 2]
```

### [EXAMPLE_TYPE_2]
```markdown
# [Another Example Title]

//example content for different scenario
[ALTERNATIVE_EXAMPLE_CONTENT]
```

## 🚀 [ADVANCED_FEATURES_SECTION]

//description of advanced features or optional sections

### [FEATURE_1_NAME]
For [use case description]:
```markdown
## [Feature Section Title]

**[Feature Description]**:
- [Feature element 1]: [Description]
- [Feature element 2]: [Description]
```

### [FEATURE_2_NAME]
For [use case description]:
```markdown
## [Another Feature Section Title]

**[Feature Description]**:
- [ ] [Checklist item 1]: [Description]
- [ ] [Checklist item 2]: [Description]
```

### [FEATURE_3_NAME]
For [use case description]:
```markdown
**🚀 [CONTINUATION_RULES_TITLE]**

✅ [POSITIVE_CONTINUATION_RULE]
✅ [ANOTHER_POSITIVE_RULE]
❌ [NEGATIVE_CONTINUATION_RULE]
❌ [ANOTHER_NEGATIVE_RULE]
```

---

## Template Customization Notes

//instructions for customizing this prompt template

**Required Customizations for Prompt Creation:**
- [ ] Replace [PROMPT_PURPOSE] with specific prompt function (e.g., "Code Assistant", "Documentation Generator")
- [ ] Update [PERFORMING_TASK] with the main task description
- [ ] Define all [REQUIREMENT_X] placeholders with actual documentation references
- [ ] Customize the MANDATORY SYNC PROTOCOL steps for the specific prompt workflow
- [ ] Replace [RULES_SECTION_NAME] with appropriate section title
- [ ] Fill in all ✅ Required Elements with prompt-specific requirements
- [ ] Define ❌ Avoid These Patterns with prompt-specific anti-patterns
- [ ] Update [CATEGORIZATION_SECTION] with relevant prompt categories
- [ ] Customize [INPUT_PROCESSING_SECTION] for the prompt's input handling
- [ ] Define validation checklist items for the prompt's output quality
- [ ] Add relevant examples showing proper prompt usage patterns

**Optional Customizations:**
- [ ] Add Recovery Commands section for complex prompts that may lose context
- [ ] Include Validation Strategy section for prompts requiring verification
- [ ] Add Continuation Rules for iterative processes
- [ ] Include prompt-specific advanced features
- [ ] Add debugging or troubleshooting sections

**Prompt-Specific Validation Checklist:**
- [ ] Follows established prompt naming conventions ([purpose]-prompt.md)
- [ ] Uses consistent emoji patterns (📚 📋 🔄 ‼️ ✅ ❌ 🎯 🔧 📝 🚀)
- [ ] Includes descriptive comments for guidance (//description)
- [ ] Has clear section organization with proper heading hierarchy
- [ ] Contains MANDATORY SYNC PROTOCOL with numbered steps
- [ ] Includes continuation directive (🔄 CONTINUE THE SYNC PROTOCOL...)
- [ ] References appropriate templates and documentation
- [ ] Uses proper file path formatting [./path/to/file.md]
- [ ] Follows markdown formatting standards for prompts
- [ ] Maintains compatibility with existing prompt patterns

**Prompt Pattern Examples:**
- **Documentation Prompts**: Focus on creating/updating docs, reference templates heavily
- **Implementation Prompts**: Focus on code creation, include testing and validation
- **Analysis Prompts**: Focus on examining and reporting, include structured output
- **Assistant Prompts**: Focus on guiding users through processes, include step-by-step protocols
