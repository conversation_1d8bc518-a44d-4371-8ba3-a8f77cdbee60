# [TEMPLATE_NAME] Template

// This is a generic template skeleton that follows established patterns from .docs/ai/templates/
// Replace [PLACEHOLDERS] with actual content and customize sections as needed
// Based on templater-prompt.md guidelines and existing template patterns

## Overview

//description of what this template is for and its main use cases

---

## [MAIN_SECTION_1]

//description of what goes in this section

### [SUBSECTION_NAME]

- [ITEM_1]: //description or details
- [ITEM_2]: //description or details
- [ITEM_3]: //description or details

**[COMPONENT_OR_FEATURE_NAME]**

//description of component, feature, or concept

**Core Functionality: [COMPONENT_NAME]**

- [Functionality 1]: //functionality description
- [Functionality 2]: //functionality description
- [Functionality 3]: //functionality description

---

## [MAIN_SECTION_2]

//description of what goes in this section

### [SUBSECTION_WITH_TABLE]

| [COLUMN_1] | [COLUMN_2] |
|--|--|
| [ROW_1_COL_1] | [ROW_1_COL_2] |
| [ROW_2_COL_1] | [ROW_2_COL_2] |

### [SUBSECTION_WITH_CODE]

```[LANGUAGE]
// add code example or configuration here
[CODE_PLACEHOLDER]
```

---

## [DIAGRAM_SECTION]

//description of the diagram and its purpose

**[DIAGRAM_TYPE] Diagram: [DIAGRAM_PURPOSE]**

//short description of the diagram below

```mermaid
---
title: [DESCRIPTIVE_TITLE]
---
[DIAGRAM_TYPE] TD
    [START_NODE] --> [END_NODE]
    // add more diagram elements here
```

---

## [PROCESS_OR_WORKFLOW_SECTION]

//description of process, workflow, or step-by-step instructions

### [PROCESS_NAME]

- [ ] **[STEP_1]**: [Step description]
  - [ ] [Sub-step 1]: //details
  - [ ] [Sub-step 2]: //details
- [ ] **[STEP_2]**: [Step description]
  - [ ] [Sub-step 1]: //details
  - [ ] [Sub-step 2]: //details

### [ALTERNATIVE_PROCESS_FORMAT]

**[PROCESS_STAGE_1]**

```sh
// add commands or code here
[COMMAND_PLACEHOLDER]
```

**[PROCESS_STAGE_2]**

```sh
// add commands or code here
[COMMAND_PLACEHOLDER]
```

---

## [CONFIGURATION_OR_TOOLS_SECTION]

//description of tools, configurations, or requirements

### Tool: [Tool Name]

**Requirements**
//provide a list of requirements to use this tool
- [Requirement 1]
- [Requirement 2]

**Configurations [Tool Name]**
//provide list of configuration files for the tool
- `[./path/to/config]`: //description of config and what it maintains
- `[./path/to/config]`: //description of config and what it maintains

---

## [EXAMPLE_USAGE_SECTION]

//description of how to use this template or examples

### Example: [Example Name]

```markdown
# [Example Title]

//example content showing proper usage patterns
[EXAMPLE_CONTENT]
```

### Example: [Another Example Name]

- [Example Item 1]: //example description
- [Example Item 2]: //example description

---

## [REFERENCE_OR_RESEARCH_SECTION]

//description of references, research, or additional resources

### [REFERENCE_TYPE]

Find all the [reference type] related to this [template purpose] in the directory [./path/to/references/].

NOTE: !! do not change this.

**‼️ Rules**

- ✅ Always provide link or path reference to resources used from this [Reference Type]. Use Oxford Academic citing style, both inline and as a footnote.
- ✅ Always prefer [reference type] and documentation provided here over your own knowledge.

**📝 Notes**

//description of notes or documentation available
A set of notes, as markdown files of [reference type] relating to topics relevant to this [template purpose] can be found in [./path/to/notes/]. Reference these to inform implementations related to this [template purpose].

---

## [FIXED_SECTION_EXAMPLE]

//description of section that should not be changed
NOTE: !! do not change this.

**[Fixed Content Title]**

- [Fixed Rule 1]: //description that should remain constant
- [Fixed Rule 2]: //description that should remain constant
- //add more fixed content here

---

## [TIPS_OR_BEST_PRACTICES_SECTION]

//description of tips, best practices, or guidelines
NOTE: !! do not change this.

- [Tip 1]: //description of best practice or tip
- [Tip 2]: //description of best practice or tip
- //add more tips here

---

## Template Customization Notes

//instructions for customizing this template

**Required Customizations:**
- [ ] Replace all [PLACEHOLDER] values with actual content
- [ ] Update section titles to match your specific use case
- [ ] Add or remove sections as needed for your template purpose
- [ ] Include relevant Mermaid diagrams with proper titles
- [ ] Add example content showing proper usage patterns

**Optional Customizations:**
- [ ] Add project-specific sections
- [ ] Include additional diagram types (flowchart, stateDiagram, erDiagram, etc.)
- [ ] Add tool-specific configuration sections
- [ ] Include reference sections for research or documentation

**Validation Checklist:**
- [ ] Follows established naming conventions (__template_name.md)
- [ ] Uses consistent placeholder patterns ([PLACEHOLDER_NAME])
- [ ] Includes descriptive comments for guidance (//description)
- [ ] Has clear section organization with proper heading hierarchy
- [ ] Contains relevant example content
- [ ] Follows markdown formatting standards
- [ ] Maintains compatibility with existing template patterns
