# Template Creator Assistant

## 📚 REQUIRED DOCUMENTATION

NOTE: Always follow the [.docs/ai/templates/*.md] when creating new templates.
Do not include the ./.docs folder in any code analysis or repo analysis

- Template structure patterns are defined in [.docs/ai/templates/*.md]
- Existing templates provide the foundation for new template creation
- Custom templates can use [.docs/ai/templates/__template.md] as a starting point

## 🔄 MANDATORY SYNC PROTOCOL

1. Before creating templates read all current [.docs/ai/templates/*.md] to understand existing patterns and structures.

2. Analyze the user's template requirements and determine if it should:
   - **Piggyback off existing template**: Modify an existing template
   - **Create new template**: Build from scratch using established templating rules

3. If piggybacking off existing template:
   - Identify the closest matching template from [.docs/ai/templates/*.md]
   - Preserve the core structure and placeholder patterns
   - Adapt and/or create sections to match the new template requirements
   - Maintain consistency with existing template naming conventions

4. If creating new template:
   - Follow the established template structure patterns
   - Use consistent placeholder naming: `[PLACEHOLDER_NAME]`
   - Include descriptive instructional comments: `//description of what goes here`
   - Add example sections where helpful
   - Include Mermaid diagram templates if applicable

5. Apply template creation rules and validate the new template structure.

6. Save the new template to [.docs/ai/templates/__TEMPLATE_NAME.md] following the naming convention.

🔄 CONTINUE THE SYNC PROTOCOL UNTIL THE TEMPLATE IS COMPLETE AND FOLLOWS ALL ESTABLISHED PATTERNS.

## ‼️ Template Creation Rules

### ✅ Required Elements
- **Title with placeholder**: Use `[PROJECT NAME]` or similar descriptive placeholder
- **Descriptive comments**: Use `//description` or `// add details here` format
- **Structured sections**: Consistent heading hierarchy (##, ###, etc.)
- **Placeholder patterns**: Use `[PLACEHOLDER]` format for all replaceable content
- **Example sections**: Include examples showing how to fill in the template
- **Mermaid diagram templates**: Include basic structure with title directive

### ✅ Naming Conventions
- **File naming**: `__TEMPLATE_NAME.md` or `__template_name.md` (double underscore prefix)
- **Placeholder naming**: `[DESCRIPTIVE_NAME]` in ALL_CAPS with underscores
- **Section naming**: Use clear, descriptive headings
- **Tool/Component naming**: `[Tool name]`, `[Component Name]` format

### ✅ Structure Patterns
- **Header section**: Title and overview description
- **Main content sections**: Organized by logical groupings
- **Placeholder sections**: Clearly marked areas for customization
- **Fixed sections**: Mark with "NOTE: !! do not change this." if applicable
- **Example sections**: Show proper usage patterns

### ❌ Avoid These Patterns
- **Generic descriptions**: Templates should be specific and actionable
- **Hardcoded values**: Use placeholders for all project-specific content
- **Inconsistent formatting**: Follow established markdown patterns
- **Missing placeholders**: All customizable content must be clearly marked
- **Complex nested structures**: Keep templates simple and clear

## 📋 Template Types and Patterns

### Documentation Templates
Based on `__COLLABORATION.md`, `__architecture.md`, `__use_cases.md`:
- Project overview and structure
- Technical specifications and diagrams
- Process documentation and workflows

### Planning Templates
Based on `__plan.md`:
- Implementation strategies
- Task breakdowns and checklists
- Status tracking and milestones

### Custom Templates
For new template types:
- Follow existing structural patterns
- Include appropriate placeholder sections
- Add relevant example content
- Maintain consistency with template library

## 🎯 User Input Processing

When user specifies template requirements, extract:

1. **Template Purpose**: What the template will be used for
2. **Base Template**: Which existing template to extend (if any)
3. **Required Sections**: What sections the template needs
4. **Placeholder Content**: What content areas need to be customizable
5. **Special Requirements**: Any unique formatting or structure needs

## 🔧 Template Validation Checklist

Before finalizing a template, verify:

- [ ] Follows established naming conventions
- [ ] Uses consistent placeholder patterns
- [ ] Includes descriptive comments for guidance
- [ ] Has clear section organization
- [ ] Contains relevant example content
- [ ] Follows markdown formatting standards
- [ ] Maintains compatibility with existing templates
- [ ] Includes all required placeholder sections

## 📝 Template Creation Examples

### Extending Existing Template
```markdown
# [PROJECT NAME] Custom Documentation

// Based on __COLLABORATION.md template with additional sections

## Overview
//description of the project and its main use cases

## [CUSTOM_SECTION_NAME]
//description of new section specific to this template type

[Continue with adapted structure...]
```

### New Template Pattern
```markdown
# [TEMPLATE_PURPOSE] Template

## [MAIN_SECTION]
//description of what goes in this section

### [SUBSECTION_NAME]
- [ITEM_1]: description
- [ITEM_2]: description

## Example Usage
//show how to use this template

[Example content...]
```
